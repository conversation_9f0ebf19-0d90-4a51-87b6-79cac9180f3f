'use client';
import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Toast, ToastType } from './toast';
export interface ToastItem {
  id: string;
  type: ToastType;
  message: string;
  duration?: number;
  customClass?: string;
}
interface ToastContainerProps {
  toasts: ToastItem[];
  removeToast: (id: string) => void;
}
export const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  removeToast,
}) => {
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null);
  useEffect(() => {
    let element = document.getElementById('toast-container');
    if (!element) {
      element = document.createElement('div');
      element.id = 'toast-container';
      element.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
      document.body.appendChild(element);
    }
    setPortalElement(element);
    return () => {
      if (element && element.parentElement) {
        element.parentElement.removeChild(element);
      }
    };
  }, []);
  if (!portalElement) return null;
  return createPortal(
    <>
      {toasts.map((toast) => (
        <div key={toast.id} className="animate-fade-in">
          <Toast
            type={toast.type}
            message={toast.message}
            onClose={() => removeToast(toast.id)}
            duration={toast.duration}
            customClass={toast.customClass}
          />
        </div>
      ))}
    </>,
    portalElement
  );
};
